<?php

namespace Tests\Feature;

use Bo<PERSON>ble\Base\Enums\BaseStatusEnum;
use Bo<PERSON>ble\Seeding\Models\Category;
use Bo<PERSON>ble\Seeding\Models\PackageList;
use Bo<PERSON>ble\Seeding\Models\Platform;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class SeedingCrudTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
    }

    public function test_platform_model_creation(): void
    {
        // Test create platform
        $platform = Platform::create([
            'name' => 'Test Platform',
            'description' => 'Test platform description',
            'status' => BaseStatusEnum::PUBLISHED,
        ]);

        $this->assertNotNull($platform);
        $this->assertEquals('Test Platform', $platform->name);
        $this->assertEquals('Test platform description', $platform->description);
        $this->assertEquals(BaseStatusEnum::PUBLISHED, $platform->status);

        // Test platform exists in database
        $this->assertDatabaseHas('sd_platforms', [
            'name' => 'Test Platform',
            'description' => 'Test platform description',
        ]);
    }

    public function test_category_model_creation(): void
    {
        // Create a platform first
        $platform = Platform::create([
            'name' => 'Test Platform',
            'description' => 'Test platform',
            'status' => BaseStatusEnum::PUBLISHED,
        ]);

        // Test create category
        $category = Category::create([
            'name' => 'Test Category',
            'description' => 'Test category description',
            'platform_id' => $platform->id,
            'status' => BaseStatusEnum::PUBLISHED,
        ]);

        $this->assertNotNull($category);
        $this->assertEquals('Test Category', $category->name);
        $this->assertEquals($platform->id, $category->platform_id);

        // Test relationship
        $this->assertEquals($platform->id, $category->platform->id);
        $this->assertEquals($platform->name, $category->platform->name);

        // Test platform has categories
        $this->assertTrue($platform->categories->contains($category));

        // Test database
        $this->assertDatabaseHas('sd_categories', [
            'name' => 'Test Category',
            'platform_id' => $platform->id,
        ]);
    }

    public function test_package_list_model_creation(): void
    {
        // Test create package list
        $packageList = PackageList::create([
            'name' => 'Test Package List',
            'value' => 'test-package-list',
            'status' => BaseStatusEnum::PUBLISHED,
        ]);

        $this->assertNotNull($packageList);
        $this->assertEquals('Test Package List', $packageList->name);
        $this->assertEquals('test-package-list', $packageList->value);
        $this->assertEquals(BaseStatusEnum::PUBLISHED, $packageList->status);

        // Test database
        $this->assertDatabaseHas('sd_package_lists', [
            'name' => 'Test Package List',
            'value' => 'test-package-list',
        ]);
    }

    public function test_models_relationships(): void
    {
        // Create platform
        $platform = Platform::create([
            'name' => 'Facebook',
            'description' => 'Facebook platform',
            'status' => BaseStatusEnum::PUBLISHED,
        ]);

        // Create category
        $category = Category::create([
            'name' => 'Likes',
            'description' => 'Facebook likes',
            'platform_id' => $platform->id,
            'status' => BaseStatusEnum::PUBLISHED,
        ]);

        // Test relationships work
        $this->assertEquals($platform->id, $category->platform->id);
        $this->assertTrue($platform->categories->contains($category));
        $this->assertEquals(1, $platform->categories->count());
    }
}
