<?php

use Illuminate\Support\Facades\Route;
use Botble\Base\Facades\AdminHelper;

Route::group(['namespace' => 'Botble\Seeding\Http\Controllers\SupperAdmin'], function (): void {
    AdminHelper::registerRoutes(function (): void {
        Route::group(['prefix' => 'seeding', 'as' => 'seeding.'], function (): void {
            Route::group(['prefix' => 'categories', 'as' => 'categories.'], function (): void {
                Route::resource('', 'CategoryController')->except(['create', 'store'])->parameters(['' => 'category']);
            });
        });
    });
});
