<?php

namespace Bo<PERSON>ble\Seeding\Http\Controllers\SupperAdmin;

use Bo<PERSON>ble\Base\Http\Actions\DeleteResourceAction;
use Bo<PERSON>ble\Base\Http\Controllers\BaseController;
use Bo<PERSON>ble\Base\Supports\Breadcrumb;
use Bo<PERSON>ble\Seeding\Forms\PackageListForm;
use Bo<PERSON>ble\Seeding\Http\Requests\PackageListRequest;
use Botble\Seeding\Models\PackageList;
use Bo<PERSON>ble\Seeding\Tables\PackageListTable;

class PackageListController extends BaseController
{
    protected function breadcrumb(): Breadcrumb
    {
        return parent::breadcrumb()
            ->add(trans('plugins/seeding::package-list.name'), route('seeding.package-lists.index'));
    }

    public function index(PackageListTable $table)
    {
        $this->pageTitle(trans('plugins/seeding::package-list.name'));

        return $table->renderTable();
    }

    public function create()
    {
        $this->pageTitle(trans('plugins/seeding::package-list.create'));

        return PackageListForm::create()->renderForm();
    }

    public function store(PackageListRequest $request)
    {
        $form = PackageListForm::create()
            ->setRequest($request)
            ->save();

        return $this
            ->httpResponse()
            ->setPreviousRoute('seeding.package-lists.index')
            ->setNextRoute('seeding.package-lists.edit', $form->getModel()->getKey())
            ->withCreatedSuccessMessage();
    }

    public function edit(PackageList $packageList)
    {
        $this->pageTitle(trans('core/base::forms.edit_item', ['name' => $packageList->name]));

        return PackageListForm::createFromModel($packageList)->renderForm();
    }

    public function update(PackageList $packageList, PackageListRequest $request)
    {
        PackageListForm::createFromModel($packageList)
            ->setRequest($request)
            ->save();

        return $this
            ->httpResponse()
            ->setPreviousRoute('seeding.package-lists.index')
            ->withUpdatedSuccessMessage();
    }

    public function destroy(PackageList $packageList): DeleteResourceAction
    {
        return DeleteResourceAction::make($packageList);
    }
}
