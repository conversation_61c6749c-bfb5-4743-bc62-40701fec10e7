<?php

namespace Bo<PERSON>ble\Seeding\Http\Controllers\SupperAdmin;

use Bo<PERSON>ble\Base\Http\Actions\DeleteResourceAction;
use Bo<PERSON>ble\Base\Http\Controllers\BaseController;
use Bo<PERSON>ble\Base\Supports\Breadcrumb;
use Bo<PERSON>ble\Seeding\Forms\PlatformForm;
use Bo<PERSON>ble\Seeding\Http\Requests\PlatformRequest;
use Botble\Seeding\Models\Platform;
use Bo<PERSON>ble\Seeding\Tables\PlatformTable;

class PlatformController extends BaseController
{
    protected function breadcrumb(): Breadcrumb
    {
        return parent::breadcrumb()
            ->add(trans('plugins/seeding::platform.name'), route('seeding.platforms.index'));
    }

    public function index(PlatformTable $table)
    {
        $this->pageTitle(trans('plugins/seeding::platform.name'));

        return $table->renderTable();
    }

    public function create()
    {
        $this->pageTitle(trans('plugins/seeding::platform.create'));

        return PlatformForm::create()->renderForm();
    }

    public function store(PlatformRequest $request)
    {
        $form = PlatformForm::create()
            ->setRequest($request)
            ->save();

        return $this
            ->httpResponse()
            ->setPreviousRoute('seeding.platforms.index')
            ->setNextRoute('seeding.platforms.edit', $form->getModel()->getKey())
            ->withCreatedSuccessMessage();
    }

    public function edit(Platform $platform)
    {
        $this->pageTitle(trans('core/base::forms.edit_item', ['name' => $platform->name]));

        return PlatformForm::createFromModel($platform)->renderForm();
    }

    public function update(Platform $platform, PlatformRequest $request)
    {
        PlatformForm::createFromModel($platform)
            ->setRequest($request)
            ->save();

        return $this
            ->httpResponse()
            ->setPreviousRoute('seeding.platforms.index')
            ->withUpdatedSuccessMessage();
    }

    public function destroy(Platform $platform): DeleteResourceAction
    {
        return DeleteResourceAction::make($platform);
    }
}
