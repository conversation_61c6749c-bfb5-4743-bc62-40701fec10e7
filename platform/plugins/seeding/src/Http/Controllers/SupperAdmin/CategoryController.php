<?php

namespace Bo<PERSON>ble\Seeding\Http\Controllers\SupperAdmin;

use Bo<PERSON>ble\Base\Http\Actions\DeleteResourceAction;
use Bo<PERSON>ble\Base\Http\Controllers\BaseController;
use Bo<PERSON>ble\Base\Supports\Breadcrumb;
use Bo<PERSON>ble\Seeding\Forms\CategoryForm;
use Bo<PERSON>ble\Seeding\Http\Requests\CategoryRequest;
use Botble\Seeding\Models\Category;
use Bo<PERSON>ble\Seeding\Tables\CategoryTable;

class CategoryController extends BaseController
{
    protected function breadcrumb(): Breadcrumb
    {
        return parent::breadcrumb()
            ->add(trans('plugins/seeding::category.name'), route('seeding.categories.index'));
    }

    public function index(CategoryTable $table)
    {
        $this->pageTitle(trans('plugins/seeding::category.name'));

        return $table->renderTable();
    }

    public function create()
    {
        $this->pageTitle(trans('plugins/seeding::category.create'));

        return CategoryForm::create()->renderForm();
    }

    public function store(CategoryRequest $request)
    {
        $form = CategoryForm::create()
            ->setRequest($request)
            ->save();

        return $this
            ->httpResponse()
            ->setPreviousRoute('seeding.categories.index')
            ->setNextRoute('seeding.categories.edit', $form->getModel()->getKey())
            ->withCreatedSuccessMessage();
    }

    public function edit(Category $category)
    {
        $this->pageTitle(trans('core/base::forms.edit_item', ['name' => $category->name]));

        return CategoryForm::createFromModel($category)->renderForm();
    }

    public function update(Category $category, CategoryRequest $request)
    {
        CategoryForm::createFromModel($category)
            ->setRequest($request)
            ->save();

        return $this
            ->httpResponse()
            ->setPreviousRoute('seeding.categories.index')
            ->withUpdatedSuccessMessage();
    }

    public function destroy(Category $category): DeleteResourceAction
    {
        return DeleteResourceAction::make($category);
    }
}
