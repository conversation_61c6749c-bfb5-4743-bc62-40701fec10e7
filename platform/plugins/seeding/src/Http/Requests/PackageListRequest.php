<?php

namespace Bo<PERSON>ble\Seeding\Http\Requests;

use Botble\Base\Enums\BaseStatusEnum;
use Botble\Support\Http\Requests\Request;
use Illuminate\Validation\Rule;

class PackageListRequest extends Request
{
    public function rules(): array
    {
        return [
            'name' => ['required', 'string', 'max:255'],
            'value' => ['required', 'string', 'max:255', Rule::unique('sd_package_lists', 'value')->ignore($this->route('package_list'))],
            'status' => ['required', Rule::in(BaseStatusEnum::values())],
        ];
    }

    public function attributes(): array
    {
        return [
            'name' => trans('plugins/seeding::package-list.name'),
            'value' => trans('plugins/seeding::package-list.value'),
            'status' => trans('core/base::tables.status'),
        ];
    }
}
