<?php

namespace Bo<PERSON>ble\Seeding\Http\Requests;

use Botble\Base\Enums\BaseStatusEnum;
use Botble\Support\Http\Requests\Request;
use Illuminate\Validation\Rule;

class CategoryRequest extends Request
{
    public function rules(): array
    {
        return [
            'name' => ['required', 'string', 'max:255', Rule::unique('sd_categories', 'name')->ignore($this->route('category'))],
            'description' => ['nullable', 'string', 'max:1000'],
            'platform_id' => ['required', 'integer', Rule::exists('sd_platforms', 'id')],
            'status' => ['required', Rule::in(BaseStatusEnum::values())],
        ];
    }

    public function attributes(): array
    {
        return [
            'name' => trans('plugins/seeding::category.name'),
            'description' => trans('plugins/seeding::category.description'),
            'platform_id' => trans('plugins/seeding::category.platform'),
            'status' => trans('core/base::tables.status'),
        ];
    }
}
