<?php

namespace Bo<PERSON>ble\Seeding\Models;

use Botble\Base\Enums\BaseStatusEnum;
use Botble\Base\Models\BaseModel;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Category extends BaseModel
{

    protected $table = 'sd_categories';

    protected $fillable = [
        'platform_id',
        'name',
        'description',
        'status',
    ];

    protected $casts = [
        'platform_id' => 'integer',
        'status' => BaseStatusEnum::class
    ];

    public function platform(): BelongsTo
    {
        return $this->belongsTo(Platform::class);
    }

    public function packages(): HasMany
    {
        return $this->hasMany(Package::class);
    }

}
