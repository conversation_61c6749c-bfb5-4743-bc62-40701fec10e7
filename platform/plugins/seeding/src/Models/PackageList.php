<?php

namespace Bo<PERSON>ble\Seeding\Models;

use Botble\Base\Enums\BaseStatusEnum;
use Botble\Base\Models\BaseModel;
use Illuminate\Database\Eloquent\Relations\HasMany;

class PackageList extends BaseModel
{
    protected $table = 'sd_package_lists';

    protected $fillable = [
        'name',
        'value',
        'status'
    ];

    protected $casts = [
        'status' => BaseStatusEnum::class,
    ];

    public function packages(): HasMany
    {
        return $this->hasMany(Package::class, 'package_list_id');
    }


}
