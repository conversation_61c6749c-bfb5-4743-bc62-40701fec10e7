<?php

namespace Bo<PERSON>ble\Seeding\Models;

use Botble\Base\Models\BaseModel;
use Botble\Seeding\Enums\MemberRoleEnum;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class TenantUser extends BaseModel
{
    public $timestamps = false;

    protected $fillable = [
        'tenant_id',
        'user_id',
        'role'
    ];

    protected $casts = [
        'role' => MemberRoleEnum::class,
    ];
    public function tenant(): BelongsTo
    {
        return $this->belongsTo(Tenant::class);
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(Member::class, 'user_id');
    }
}
