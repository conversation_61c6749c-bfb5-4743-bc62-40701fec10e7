<?php

namespace Bo<PERSON>ble\Seeding\Models;

use Botble\Base\Models\BaseModel;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Tenant extends BaseModel
{
    protected $table = 'sd_tenants';

    protected $fillable = [
        'domain',
        'status',
        'data',
    ];

    public function members(): BelongsToMany
    {
        return $this->belongsToMany(Member::class, 'tenant_users')->withPivot('role');
    }

    public function packages(): HasMany
    {
        return $this->hasMany(Package::class);
    }
}
