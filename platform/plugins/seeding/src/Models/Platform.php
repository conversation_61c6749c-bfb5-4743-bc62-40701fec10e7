<?php

namespace Bo<PERSON>ble\Seeding\Models;

use Botble\Base\Enums\BaseStatusEnum;
use Botble\Base\Models\BaseModel;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Platform extends BaseModel
{
    protected $table = 'sd_platforms';

    protected $fillable = [
        'name',
        'icon',
        'description',
        'status',
    ];

    protected $casts = [
        'status' => BaseStatusEnum::class
    ];

    public function categories(): HasMany
    {
        return $this->hasMany(Category::class);
    }

}
