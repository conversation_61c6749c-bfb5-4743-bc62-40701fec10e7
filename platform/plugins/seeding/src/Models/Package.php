<?php

namespace Bo<PERSON>ble\Seeding\Models;

use Botble\Base\Models\BaseModel;
use Botble\Seeding\Enums\PackageStatusEnum;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Package extends BaseModel
{
    protected $table = 'sd_packages';

    protected $fillable = [
        'tenant_id',
        'package_list_id',
        'category_id',
        'info',
        'mode',
        'rate_member',
        'rate_collaborator',
        'rate_agency',
        'rate_distributor',
        'min',
        'max',
        'allow_reaction',
        'allow_comment',
        'allow_gender',
        'get_uid',
        'is_refund',
        'is_warranty',
        'allow_cancel',
        'description',
        'status',
        'visibility',
    ];

    protected $casts = [
        'status' => PackageStatusEnum::class
    ];

    public function tenant(): BelongsTo
    {
        return $this->belongsTo(Tenant::class, 'tenant_id');
    }

    public function packageList(): BelongsTo
    {
        return $this->belongsTo(PackageList::class, 'package_id');
    }

    public function category(): BelongsTo
    {
        return $this->belongsTo(Category::class, 'category_id');
    }
}
