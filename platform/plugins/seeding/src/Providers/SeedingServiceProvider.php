<?php

namespace Bo<PERSON>ble\Seeding\Providers;

use Botble\Base\Facades\DashboardMenu;
use Bo<PERSON>ble\Base\Supports\DashboardMenuItem;
use Bo<PERSON>ble\Base\Supports\ServiceProvider;
use Bo<PERSON>ble\Base\Traits\LoadAndPublishDataTrait;

class SeedingServiceProvider extends ServiceProvider
{
    use LoadAndPublishDataTrait;

    public function boot(): void
    {
        $this
            ->setNamespace('plugins/seeding')
            ->loadHelpers()
            ->loadRoutes()
            ->loadAndPublishViews()
            ->loadAndPublishTranslations()
            ->loadAndPublishConfigurations(['permissions'])
            ->loadMigrations()
            ->publishAssets();


        DashboardMenu::default()->beforeRetrieving(function (): void {
            DashboardMenu::make()
                ->registerItem(
                    DashboardMenuItem::make()
                        ->id('cms-plugins-seeding')
                        ->priority(0)
                        ->name('plugins/seeding::seeding.name')
                        ->icon('ti ti-rocket')
                )
                ->registerItem(
                    DashboardMenuItem::make()
                        ->id('cms-plugins-seeding-order')
                        ->parentId('cms-plugins-seeding')
                        ->priority(120)
                        ->name('plugins/seeding::seeding.order.name')
                        ->route('seeding.categories.index')
                )
                ->registerItem(
                    DashboardMenuItem::make()
                        ->id('cms-plugins-seeding-category')
                        ->parentId('cms-plugins-seeding')
                        ->priority(120)
                        ->name('plugins/seeding::seeding.category.name')
                        ->route('seeding.categories.index')
                )
                ->registerItem(
                    DashboardMenuItem::make()
                        ->id('cms-plugins-seeding-package')
                        ->parentId('cms-plugins-seeding')
                        ->priority(120)
                        ->name('plugins/seeding::seeding.package.name')
                        ->route('seeding.categories.index')
                )
                ->registerItem(
                    DashboardMenuItem::make()
                        ->id('cms-plugins-seeding-transaction')
                        ->parentId('cms-plugins-seeding')
                        ->priority(120)
                        ->name('plugins/seeding::seeding.transaction.name')
                        ->route('seeding.categories.index')
                )
                ->registerItem(
                    DashboardMenuItem::make()
                        ->id('cms-plugins-seeding-bank')
                        ->parentId('cms-plugins-seeding')
                        ->priority(120)
                        ->name('plugins/seeding::seeding.bank.name')
                        ->route('seeding.categories.index')
                )
                ->registerItem(
                    DashboardMenuItem::make()
                        ->id('cms-plugins-seeding-member')
                        ->parentId('cms-plugins-seeding')
                        ->priority(120)
                        ->name('plugins/seeding::seeding.member.name')
                        ->route('seeding.categories.index')
                );
        });
        DashboardMenu::default()->beforeRetrieving(function (): void {
            DashboardMenu::make()
                ->registerItem(
                    DashboardMenuItem::make()
                        ->id('cms-plugins-tenant')
                        ->priority(0)
                        ->name('plugins/seeding::seeding.tenant.name')
                        ->icon('ti ti-world-www')
                )
                ->registerItem(
                    DashboardMenuItem::make()
                        ->id('cms-plugins-tenant-report')
                        ->parentId('cms-plugins-tenant')
                        ->priority(120)
                        ->name('plugins/seeding::seeding.report.name')
                        ->route('seeding.categories.index')
                )
                ->registerItem(
                    DashboardMenuItem::make()
                        ->id('cms-plugins-tenant-list')
                        ->parentId('cms-plugins-tenant')
                        ->priority(120)
                        ->name('plugins/seeding::seeding.tenant.list')
                        ->route('seeding.categories.index')
                )
                ->registerItem(
                    DashboardMenuItem::make()
                        ->id('cms-plugins-tenant-setting')
                        ->parentId('cms-plugins-tenant')
                        ->priority(120)
                        ->name('plugins/seeding::seeding.tenant.setting.name')
                        ->route('seeding.categories.index')
                )
                ;
        });
    }
}
