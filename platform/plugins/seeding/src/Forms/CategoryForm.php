<?php

namespace Bo<PERSON>ble\Seeding\Forms;

use Bo<PERSON>ble\Base\Forms\FieldOptions\DescriptionFieldOption;
use Botble\Base\Forms\FieldOptions\NameFieldOption;
use Bo<PERSON>ble\Base\Forms\FieldOptions\SelectFieldOption;
use Bo<PERSON>ble\Base\Forms\FieldOptions\StatusFieldOption;
use Botble\Base\Forms\Fields\SelectField;
use Botble\Base\Forms\Fields\TextareaField;
use Botble\Base\Forms\Fields\TextField;
use Botble\Base\Forms\FormAbstract;
use Bo<PERSON>ble\Seeding\Http\Requests\CategoryRequest;
use Botble\Seeding\Models\Category;
use Botble\Seeding\Models\Platform;

class CategoryForm extends FormAbstract
{
    public function setup(): void
    {
        $this
            ->model(Category::class)
            ->setValidatorClass(CategoryRequest::class)
            ->add('name', TextField::class, NameFieldOption::make()->required()->maxLength(255))
            ->add('description', TextareaField::class, DescriptionFieldOption::make())
            ->add(
                'platform_id',
                SelectField::class,
                SelectFieldOption::make()
                    ->label(trans('plugins/seeding::category.platform'))
                    ->required()
                    ->choices($this->getPlatformChoices())
                    ->searchable()
            )
            ->add('status', SelectField::class, StatusFieldOption::make())
            ->setBreakFieldPoint('status');
    }

    protected function getPlatformChoices(): array
    {
        return Platform::query()
            ->wherePublished()
            ->pluck('name', 'id')
            ->all();
    }
}
