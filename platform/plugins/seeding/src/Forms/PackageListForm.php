<?php

namespace Bo<PERSON><PERSON>\Seeding\Forms;

use Bo<PERSON>ble\Base\Forms\FieldOptions\NameFieldOption;
use Bo<PERSON>ble\Base\Forms\FieldOptions\StatusFieldOption;
use Bo<PERSON>ble\Base\Forms\FieldOptions\TextFieldOption;
use Bo<PERSON>ble\Base\Forms\Fields\SelectField;
use Botble\Base\Forms\Fields\TextField;
use Botble\Base\Forms\FormAbstract;
use Botble\Seeding\Http\Requests\PackageListRequest;
use Botble\Seeding\Models\PackageList;

class PackageListForm extends FormAbstract
{
    public function setup(): void
    {
        $this
            ->model(PackageList::class)
            ->setValidatorClass(PackageListRequest::class)
            ->add('name', TextField::class, NameFieldOption::make()->required()->maxLength(255))
            ->add(
                'value',
                TextField::class,
                TextFieldOption::make()
                    ->label(trans('plugins/seeding::package-list.value'))
                    ->required()
                    ->maxLength(255)
                    ->helperText(trans('plugins/seeding::package-list.value_help'))
            )
            ->add('status', SelectField::class, StatusFieldOption::make())
            ->setBreakFieldPoint('status');
    }
}
