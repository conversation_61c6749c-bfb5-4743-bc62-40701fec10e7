<?php

namespace Bo<PERSON><PERSON>\Seeding\Tables;

use Bo<PERSON>ble\Base\Facades\BaseHelper;
use Bo<PERSON>ble\Seeding\Models\Category;
use Botble\Table\Abstracts\TableAbstract;
use Botble\Table\Actions\DeleteAction;
use Botble\Table\Actions\EditAction;
use Bo<PERSON>ble\Table\BulkActions\DeleteBulkAction;
use Botble\Table\BulkChanges\CreatedAtBulkChange;
use Botble\Table\BulkChanges\NameBulkChange;
use Botble\Table\BulkChanges\StatusBulkChange;
use Botble\Table\Columns\Column;
use Botble\Table\Columns\CreatedAtColumn;
use Botble\Table\Columns\IdColumn;
use Botble\Table\Columns\NameColumn;
use Botble\Table\Columns\StatusColumn;
use Botble\Table\HeaderActions\CreateHeaderAction;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\Relation;
use Illuminate\Database\Query\Builder as QueryBuilder;

class CategoryTable extends TableAbstract
{
    public function setup(): void
    {
        $this
            ->model(Category::class)
            ->addHeaderAction(CreateHeaderAction::make()->route('seeding.categories.create'))
            ->addColumns([
                IdColumn::make(),
                NameColumn::make()->route('seeding.categories.edit'),
                Column::make('platform.name')
                    ->title(trans('plugins/seeding::category.platform'))
                    ->alignStart(),
                Column::make('description')
                    ->title(trans('plugins/seeding::category.description'))
                    ->alignStart()
                    ->limit(50),
                CreatedAtColumn::make(),
                StatusColumn::make(),
            ])
            ->addActions([
                EditAction::make()->route('seeding.categories.edit'),
                DeleteAction::make()->route('seeding.categories.destroy'),
            ])
            ->addBulkActions([
                DeleteBulkAction::make()->permission('seeding.categories.destroy'),
            ])
            ->addBulkChanges([
                NameBulkChange::make(),
                StatusBulkChange::make(),
                CreatedAtBulkChange::make(),
            ])
            ->queryUsing(function (Builder $query) {
                return $query
                    ->select([
                        'id',
                        'name',
                        'description',
                        'platform_id',
                        'created_at',
                        'status',
                    ])
                    ->with(['platform:id,name']);
            });
    }
}
