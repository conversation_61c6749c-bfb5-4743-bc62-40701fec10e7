<?php

namespace Bo<PERSON><PERSON>\Seeding\Tables;

use Bo<PERSON>ble\Base\Facades\BaseHelper;
use <PERSON><PERSON>ble\Seeding\Models\PackageList;
use Bo<PERSON>ble\Table\Abstracts\TableAbstract;
use Botble\Table\Actions\DeleteAction;
use Botble\Table\Actions\EditAction;
use Bo<PERSON>ble\Table\BulkActions\DeleteBulkAction;
use Botble\Table\BulkChanges\CreatedAtBulkChange;
use Botble\Table\BulkChanges\NameBulkChange;
use Botble\Table\BulkChanges\StatusBulkChange;
use Botble\Table\Columns\Column;
use Botble\Table\Columns\CreatedAtColumn;
use Botble\Table\Columns\IdColumn;
use Botble\Table\Columns\NameColumn;
use Botble\Table\Columns\StatusColumn;
use Botble\Table\HeaderActions\CreateHeaderAction;
use Illuminate\Database\Eloquent\Builder;

class PackageListTable extends TableAbstract
{
    public function setup(): void
    {
        $this
            ->model(PackageList::class)
            ->addHeaderAction(CreateHeaderAction::make()->route('seeding.package-lists.create'))
            ->addColumns([
                IdColumn::make(),
                NameColumn::make()->route('seeding.package-lists.edit'),
                Column::make('value')
                    ->title(trans('plugins/seeding::package-list.value'))
                    ->alignStart(),
                Column::make('packages_count')
                    ->title(trans('plugins/seeding::package-list.packages_count'))
                    ->alignCenter()
                    ->getValueUsing(function (Column $column) {
                        return number_format($column->getItem()->packages_count);
                    }),
                CreatedAtColumn::make(),
                StatusColumn::make(),
            ])
            ->addActions([
                EditAction::make()->route('seeding.package-lists.edit'),
                DeleteAction::make()->route('seeding.package-lists.destroy'),
            ])
            ->addBulkActions([
                DeleteBulkAction::make()->permission('seeding.package-lists.destroy'),
            ])
            ->addBulkChanges([
                NameBulkChange::make(),
                StatusBulkChange::make(),
                CreatedAtBulkChange::make(),
            ])
            ->queryUsing(function (Builder $query) {
                return $query
                    ->select([
                        'id',
                        'name',
                        'value',
                        'created_at',
                        'status',
                    ])
                    ->withCount('packages');
            });
    }
}
