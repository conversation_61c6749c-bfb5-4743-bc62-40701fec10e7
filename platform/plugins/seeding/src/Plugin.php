<?php

namespace Botble\Seeding;

use Botble\PluginManagement\Abstracts\PluginOperationAbstract;
use Illuminate\Support\Facades\Schema;

class Plugin extends PluginOperationAbstract
{
    public static function remove(): void
    {
        Schema::disableForeignKeyConstraints();
        Schema::dropIfExists('sd_packages');
        Schema::dropIfExists('sd_categories');
        Schema::dropIfExists('sd_platforms');
        Schema::dropIfExists('sd_package_lists');
        Schema::dropIfExists('sd_tenants');
        Schema::dropIfExists('sd_tenant_users');
        Schema::dropIfExists('sd_members');
        Schema::dropIfExists('sd_member_activity_logs');
    }
}
