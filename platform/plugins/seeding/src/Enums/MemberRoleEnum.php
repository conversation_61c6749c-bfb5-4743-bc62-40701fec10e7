<?php

namespace Bo<PERSON>ble\Seeding\Enums;

use Botble\Base\Facades\Html;
use Botble\Base\Supports\Enum;
use Illuminate\Support\HtmlString;

/**
 * @method static MemberRoleEnum USER()
 * @method static MemberRoleEnum ADMIN()
 */
class MemberRoleEnum extends Enum
{
    public const USER = 'member';
    public const ADMIN = 'admin';

    public static $langPath = 'plugins/seeding::enums.roles';

    public function toHtml(): string|HtmlString
    {
        return match ($this->value) {
            self::ADMIN => Html::tag('span', self::ADMIN()->label(), ['class' => 'badge bg-success text-success-fg']),
            self::USER => Html::tag('span', self::USER()->label(), ['class' => 'badge bg-info text-info-fg']),
            default => parent::toHtml(),
        };
    }
}
