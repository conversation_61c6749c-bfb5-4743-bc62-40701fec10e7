<?php

namespace Bo<PERSON>ble\Seeding\Enums;

use Botble\Base\Facades\Html;
use Botble\Base\Supports\Enum;
use Illuminate\Support\HtmlString;

/**
 * @method static PackageStatusEnum ACTIVE()
 * @method static PackageStatusEnum SLOW()
 * @method static PackageStatusEnum PAUSE()
 * @method static PackageStatusEnum STOPPED()
 */
class PackageStatusEnum extends Enum
{
    public const ACTIVE = 'active';
    public const SLOW = 'slow';
    public const PAUSE = 'pause';
    public const STOPPED = 'stopped';

    public static $langPath = 'plugins/seeding::enums.packages.status';

    public function toHtml(): string|HtmlString
    {
        return match ($this->value) {
            self::SLOW => Html::tag('span', self::SLOW()->label(), ['class' => 'badge bg-warning text-warning-fg']),
            self::ACTIVE => Html::tag('span', self::ACTIVE()->label(), ['class' => 'badge bg-success text-success-fg']),
            self::PAUSE => Html::tag('span', self::PAUSE()->label(), ['class' => 'badge bg-danger text-danger-fg']),
            self::STOPPED => Html::tag('span', self::STOPPED()->label(), ['class' => 'badge bg-danger text-danger-fg']),
            default => parent::toHtml(),
        };
    }
}
