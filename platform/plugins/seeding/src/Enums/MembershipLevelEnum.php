<?php

namespace Bo<PERSON>ble\Seeding\Enums;

use Botble\Base\Facades\Html;
use Botble\Base\Supports\Enum;
use Illuminate\Support\HtmlString;

/**
 * @method static MembershipLevelEnum MEMBER()
 * @method static MembershipLevelEnum COLLABORATOR()
 * @method static MembershipLevelEnum AGENCY()
 * @method static MembershipLevelEnum DISTRIBUTOR()
 */
class MembershipLevelEnum extends Enum
{
    public const MEMBER = 'member';
    public const COLLABORATOR = 'collaborator';
    public const AGENCY = 'agency';
    public const DISTRIBUTOR = 'distributor';

    public static $langPath = 'plugins/seeding::enums.levels';

    public function toHtml(): string|HtmlString
    {
        return match ($this->value) {
            self::COLLABORATOR => Html::tag('span', self::COLLABORATOR()->label(), ['class' => 'badge bg-info text-info-fg']),
            self::AGENCY => Html::tag('span', self::AGENCY()->label(), ['class' => 'badge bg-warning text-warning-fg']),
            self::MEMBER => Html::tag('span', self::MEMBER()->label(), ['class' => 'badge bg-success text-success-fg']),
            self::DISTRIBUTOR => Html::tag('span', self::MEMBER()->label(), ['class' => 'badge bg-pink text-pink-fg']),
            default => parent::toHtml(),
        };
    }
}
